import { Component, Input, Output, EventEmitter, On<PERSON><PERSON>roy, OnInit, OnChang<PERSON>, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';

// Import new dropdown components
import { TypeDropdownComponent } from '../dropdowns/type-dropdown/type-dropdown.component';
import { ForeignKeyDropdownComponent } from '../dropdowns/foreign-key-dropdown/foreign-key-dropdown.component';
import { RegularDropdownComponent } from '../dropdowns/regular-dropdown/regular-dropdown.component';
import { DropdownEventData } from '../../../core/models/dropdown.models';

@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    TypeDropdownComponent,
    ForeignKeyDropdownComponent,
    RegularDropdownComponent
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements OnInit, OnDestroy, OnChanges {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)

  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  /**
   * Handle dropdown value changes from new dropdown components
   */
  onDropdownValueChange(eventData: DropdownEventData): void {
    // Update form control
    this.form.get(eventData.fieldName)?.setValue(eventData.value);

    // Emit field value change for parent component
    this.fieldValueChange.emit({
      fieldName: eventData.fieldName,
      value: eventData.value
    });
  }

  // Dynamic dropdown properties - EXACT from main component lines 85-96
  showTypeDropdown: { [key: string]: boolean } = {};
  filteredTypeOptions: { [key: string]: any[] } = {};
  typeSearchTimeout: { [key: string]: any } = {};

  showForeignKeyDropdown: { [key: string]: boolean } = {};
  filteredForeignKeyOptions: { [key: string]: any[] } = {};
  foreignKeySearchTimeout: { [key: string]: any } = {};

  // Regular dropdown properties (for other foreign keys)
  showRegularDropdown: { [key: string]: boolean } = {};
  filteredRegularOptions: { [key: string]: any[] } = {};
  regularSearchTimeout: { [key: string]: any } = {};

  // Performance optimization: API response cache
  private apiCache: { [key: string]: any[] } = {};

  // Track when we're setting dropdown values to prevent input conflicts
  private settingDropdownValue: { [key: string]: boolean } = {};

  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Preload all dropdown data for superior performance - matching backup strategy
    this.preloadAllDropdownData();

    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
  }

  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.field.fieldName);
    if (formControl) {
      if (this.isViewMode || this.field.noInput) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }

  ngOnChanges(): void {
    // Update form control disabled state when inputs change
    this.updateFormControlDisabledState();
  }

  ngOnDestroy() {
    // Clear type search timeouts - EXACT from main component lines 112-116
    Object.values(this.typeSearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Clear foreign key search timeouts - EXACT from main component lines 119-123
    Object.values(this.foreignKeySearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Clear regular dropdown search timeouts - EXACT from main component lines 126-130
    Object.values(this.regularSearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });
  }

  // Type dropdown methods - EXACT from main component lines 1471-1549
  onTypeInputChange(event: Event, fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    // Skip if we're currently setting a dropdown value
    if (this.settingDropdownValue[fieldName]) {
      console.log('🔧 Skipping input change - setting dropdown value');
      return;
    }

    const input = event.target as HTMLInputElement;
    const value = input.value;
    console.log('🔍 Type input change:', { fieldName, value, formValue: this.form.get(fieldName)?.value });

    // Clear previous timeout
    if (this.typeSearchTimeout[fieldName]) {
      clearTimeout(this.typeSearchTimeout[fieldName]);
    }

    // Set a new timeout to avoid too many API calls
    this.typeSearchTimeout[fieldName] = setTimeout(() => {
      this.searchTypeOptions(value, fieldName);
    }, 300);
  }

  onTypeInputFocus(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    const currentValue = this.form.get(fieldName)?.value || '';
    console.log('🔍 Type input focus:', { fieldName, currentValue, formValue: this.form.value });
    if (currentValue.trim() === '') {
      this.loadAllTypes(fieldName);
    } else {
      this.searchTypeOptions(currentValue, fieldName);
    }
  }

  onTypeInputBlur(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    const currentValue = this.form.get(fieldName)?.value || '';
    console.log('🔍 Type input blur:', { fieldName, currentValue, formValue: this.form.value });
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showTypeDropdown[fieldName] = false;
    }, 200);
  }

  toggleTypeDropdown(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    if (!this.showTypeDropdown[fieldName]) {
      const currentValue = this.form.get(fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllTypes(fieldName);
      } else {
        this.searchTypeOptions(currentValue, fieldName);
      }
    } else {
      this.showTypeDropdown[fieldName] = false;
    }
  }

  searchTypeOptions(searchTerm: string, fieldName: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllTypes(fieldName);
      return;
    }
    // fieldType API doesn't support server-side filtering, use client-side filtering
    this.loadAllAndFilter('fieldType', searchTerm, fieldName, 'type');
  }

  selectTypeOption(option: any, fieldName: string): void {
    this.setDropdownValue(option, fieldName, 'type');
  }

  loadAllTypes(fieldName: string): void {
    const cacheKey = 'fieldType';

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredTypeOptions[fieldName] = this.apiCache[cacheKey];
      this.showTypeDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredTypeOptions[fieldName] = response;
          this.showTypeDropdown[fieldName] = true;
        } else {
          this.filteredTypeOptions[fieldName] = [];
          this.showTypeDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredTypeOptions[fieldName] = [];
        this.showTypeDropdown[fieldName] = true;
      }
    });
  }

  // Foreign key dropdown methods - EXACT from main component lines 1551-1630
  onForeignKeyInputChange(event: Event, fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    // Skip if we're currently setting a dropdown value
    if (this.settingDropdownValue[fieldName]) {
      console.log('🔧 Skipping foreign key input change - setting dropdown value');
      return;
    }

    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Clear previous timeout
    if (this.foreignKeySearchTimeout[fieldName]) {
      clearTimeout(this.foreignKeySearchTimeout[fieldName]);
    }

    // Set a new timeout to avoid too many API calls
    this.foreignKeySearchTimeout[fieldName] = setTimeout(() => {
      this.searchForeignKeyOptions(value, fieldName);
    }, 300);
  }

  onForeignKeyInputFocus(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    const currentValue = this.form.get(fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllForeignKeys(fieldName);
    } else {
      this.searchForeignKeyOptions(currentValue, fieldName);
    }
  }

  onForeignKeyInputBlur(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showForeignKeyDropdown[fieldName] = false;
    }, 200);
  }

  toggleForeignKeyDropdown(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    if (!this.showForeignKeyDropdown[fieldName]) {
      const currentValue = this.form.get(fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllForeignKeys(fieldName);
      } else {
        this.searchForeignKeyOptions(currentValue, fieldName);
      }
    } else {
      this.showForeignKeyDropdown[fieldName] = false;
    }
  }

  searchForeignKeyOptions(searchTerm: string, fieldName: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllForeignKeys(fieldName);
      return;
    }
    // formDefinition API doesn't support server-side filtering, use client-side filtering
    this.loadAllAndFilter('formDefinition', searchTerm, fieldName, 'foreignKey');
  }

  selectForeignKeyOption(option: any, fieldName: string): void {
    this.setDropdownValue(option, fieldName, 'foreignKey');
  }

  loadAllForeignKeys(fieldName: string): void {
    const cacheKey = 'formDefinition';

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredForeignKeyOptions[fieldName] = this.apiCache[cacheKey];
      this.showForeignKeyDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=formDefinition`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredForeignKeyOptions[fieldName] = response;
          this.showForeignKeyDropdown[fieldName] = true;
        } else {
          this.filteredForeignKeyOptions[fieldName] = [];
          this.showForeignKeyDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredForeignKeyOptions[fieldName] = [];
        this.showForeignKeyDropdown[fieldName] = true;
      }
    });
  }

  // Regular dropdown methods (for other foreign keys) - EXACT from main component lines 1632-1722
  onRegularInputChange(event: Event, fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    // Skip if we're currently setting a dropdown value
    if (this.settingDropdownValue[fieldName]) {
      console.log('🔧 Skipping regular input change - setting dropdown value');
      return;
    }

    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Clear previous timeout
    if (this.regularSearchTimeout[fieldName]) {
      clearTimeout(this.regularSearchTimeout[fieldName]);
    }

    // Set a new timeout to avoid too many API calls
    this.regularSearchTimeout[fieldName] = setTimeout(() => {
      this.searchRegularOptions(value, fieldName);
    }, 300);
  }

  onRegularInputFocus(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    const currentValue = this.form.get(fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllRegularOptions(fieldName);
    } else {
      this.searchRegularOptions(currentValue, fieldName);
    }
  }

  onRegularInputBlur(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showRegularDropdown[fieldName] = false;
    }, 200);
  }

  toggleRegularDropdown(fieldName: string): void {
    // CRITICAL: Prevent interaction when field is disabled/read-only
    if (this.isViewMode || this.field.noInput) {
      return;
    }

    if (!this.showRegularDropdown[fieldName]) {
      const currentValue = this.form.get(fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllRegularOptions(fieldName);
      } else {
        this.searchRegularOptions(currentValue, fieldName);
      }
    } else {
      this.showRegularDropdown[fieldName] = false;
    }
  }

  searchRegularOptions(searchTerm: string, fieldName: string): void {
    // Extract the original field name from complex field names
    const originalFieldName = this.extractOriginalFieldName(fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    if (!field || !field.foreginKey) return;

    if (searchTerm.trim() === '') {
      this.loadAllRegularOptions(fieldName);
      return;
    }

    // Use client-side filtering for regular foreign keys
    this.loadAllAndFilterRegular(field.foreginKey, searchTerm, fieldName);
  }

  selectRegularOption(option: any, fieldName: string): void {
    this.setDropdownValue(option, fieldName, 'regular');
  }

  loadAllRegularOptions(fieldName: string): void {
    // Extract the original field name from complex field names
    const originalFieldName = this.extractOriginalFieldName(fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    if (!field || !field.foreginKey) return;

    const cacheKey = field.foreginKey;

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredRegularOptions[fieldName] = this.apiCache[cacheKey];
      this.showRegularDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${field.foreginKey}`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredRegularOptions[fieldName] = response;
          this.showRegularDropdown[fieldName] = true;
        } else {
          this.filteredRegularOptions[fieldName] = [];
          this.showRegularDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredRegularOptions[fieldName] = [];
        this.showRegularDropdown[fieldName] = true;
      }
    });
  }

  // Helper methods - EXACT from main component lines 1358-1393, 1724-1769
  private loadAllAndFilter(queryBuilderId: string, searchTerm: string, fieldName: string, dropdownType: 'type' | 'foreignKey'): void {
    const cacheKey = queryBuilderId;

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option =>
        option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
      );
      if (dropdownType === 'type') {
        this.filteredTypeOptions[fieldName] = filtered;
        this.showTypeDropdown[fieldName] = true;
      } else {
        this.filteredForeignKeyOptions[fieldName] = filtered;
        this.showForeignKeyDropdown[fieldName] = true;
      }
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          const filtered = response.filter(option =>
            option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
          );
          if (dropdownType === 'type') {
            this.filteredTypeOptions[fieldName] = filtered;
            this.showTypeDropdown[fieldName] = true;
          } else {
            this.filteredForeignKeyOptions[fieldName] = filtered;
            this.showForeignKeyDropdown[fieldName] = true;
          }
        } else {
          this.setEmptyDropdownState(fieldName, dropdownType);
        }
      },
      error: () => {
        this.setEmptyDropdownState(fieldName, dropdownType);
      }
    });
  }

  private setEmptyDropdownState(fieldName: string, dropdownType: 'type' | 'foreignKey'): void {
    if (dropdownType === 'type') {
      this.filteredTypeOptions[fieldName] = [];
      this.showTypeDropdown[fieldName] = true;
    } else {
      this.filteredForeignKeyOptions[fieldName] = [];
      this.showForeignKeyDropdown[fieldName] = true;
    }
  }

  private setDropdownValue(option: any, fieldName: string, dropdownType: 'type' | 'foreignKey' | 'regular'): void {
    console.log('🔧 setDropdownValue called:', {
      option,
      fieldName,
      dropdownType,
      optionROW_ID: option.ROW_ID,
      formStructure: this.form.value,
      formControls: Object.keys(this.form.controls)
    });

    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue[fieldName] = true;

    const formControl = this.form.get(fieldName);
    console.log('🔧 Form control search:', {
      fieldName,
      found: !!formControl,
      currentValue: formControl?.value,
      formControlsAvailable: Object.keys(this.form.controls),
      formValue: this.form.value
    });

    if (formControl) {
      console.log('✅ Setting form control value...');

      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);
      console.log('🔧 Display text extracted:', displayText);

      // Set the form control value to ROW_ID (for storage)
      formControl.setValue(option.ROW_ID);
      console.log('✅ Form control value set to:', option.ROW_ID, 'New value:', formControl.value);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(fieldName) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
          console.log('✅ Input display value set to:', displayText);
        }
      }, 0);

      // Force change detection and validation
      formControl.markAsDirty();
      formControl.markAsTouched();
      formControl.updateValueAndValidity();

      // Force Angular change detection to ensure input field displays the value
      this.cdr.detectChanges();
      console.log('✅ Change detection triggered, final value:', formControl.value);

      // Additional debugging: Check if the input element reflects the value
      setTimeout(() => {
        const inputElement = document.getElementById(fieldName) as HTMLInputElement;
        console.log('🔍 Input element check (100ms):', {
          fieldName,
          inputExists: !!inputElement,
          inputValue: inputElement?.value,
          formControlValue: formControl.value
        });

        // Check again after a longer delay to see if something is overriding the value
        setTimeout(() => {
          console.log('🔍 Input element check (500ms):', {
            fieldName,
            inputValue: inputElement?.value,
            formControlValue: formControl.value
          });
        }, 400);
      }, 100);
    } else {
      console.error('❌ Form control not found for fieldName:', fieldName);
      console.error('Available controls:', Object.keys(this.form.controls));
    }

    // Close the appropriate dropdown
    if (dropdownType === 'type') {
      this.showTypeDropdown[fieldName] = false;
    } else if (dropdownType === 'foreignKey') {
      this.showForeignKeyDropdown[fieldName] = false;
    } else if (dropdownType === 'regular') {
      this.showRegularDropdown[fieldName] = false;
    }

    // Also emit the change event for consistency
    this.fieldValueChange.emit({ fieldName, value: option.ROW_ID });
    console.log('📡 Emitted field value change:', { fieldName, value: option.ROW_ID });

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue[fieldName] = false;
    }, 100);
  }

  /**
   * Get the display text for an option (human-readable text to show in input)
   */
  private getOptionDisplayText(option: any): string {
    if (!option) return '';

    // For type fields, just return the ROW_ID
    if (typeof option.ROW_ID === 'string') {
      return option.ROW_ID;
    }

    // For other foreign key fields, get the first non-ROW_ID property
    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      return option[keys[0]] || option.ROW_ID || '';
    }

    return option.ROW_ID || '';
  }

  /**
   * Extract the original field name from complex field names like:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   * EXACT from main component lines 1724-1745
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  private loadAllAndFilterRegular(queryBuilderId: string, searchTerm: string, fieldName: string): void {
    const cacheKey = queryBuilderId;

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option =>
        option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
      );
      this.filteredRegularOptions[fieldName] = filtered;
      this.showRegularDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          const filtered = response.filter(option =>
            option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
          );
          this.filteredRegularOptions[fieldName] = filtered;
          this.showRegularDropdown[fieldName] = true;
        } else {
          this.filteredRegularOptions[fieldName] = [];
          this.showRegularDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredRegularOptions[fieldName] = [];
        this.showRegularDropdown[fieldName] = true;
      }
    });
  }

  // Utility method for displaying dropdown options - EXACT from main component line 1292
  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  // Performance optimization: trackBy functions - EXACT from backup line 768
  trackByOptionId(_index: number, option: any): string {
    return option.ROW_ID;
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // Helper methods from backup - EXACT implementation
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    // Trim the entire groupPath first to handle trailing spaces
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  // High-performance preloading strategy - EXACT from backup implementation approach
  private preloadAllDropdownData(): void {
    // Preload fieldType data (used by Type dropdowns)
    this.preloadDropdownData('fieldType');

    // Preload formDefinition data (used by Foreign Key dropdowns)
    this.preloadDropdownData('formDefinition');

    // Preload all unique foreign key dropdown data from fields
    if (this.fields && this.fields.length > 0) {
      const uniqueForeignKeys = new Set<string>();
      this.fields.forEach(field => {
        if (field.foreginKey && field.foreginKey !== 'fieldType' && field.foreginKey !== 'formDefinition') {
          uniqueForeignKeys.add(field.foreginKey);
        }
      });

      // Preload each unique foreign key
      uniqueForeignKeys.forEach(foreignKey => {
        this.preloadDropdownData(foreignKey);
      });
    }
  }

  private preloadDropdownData(queryBuilderId: string): void {
    const cacheKey = queryBuilderId;

    // Skip if already cached
    if (this.apiCache[cacheKey]) {
      return;
    }

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }
}
