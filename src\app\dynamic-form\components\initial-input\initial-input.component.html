<div class="initial-input">
  <form [formGroup]="form" class="form-container">
    <div class="form-main-field">
      <label for="ID" class="form-label">ID</label>
      <div class="input-button-group">
        <!-- Use new ID dropdown component -->
        <app-id-dropdown
          fieldName="ID"
          [value]="form.get('ID')?.value"
          [tableName]="tableName"
          [screenName]="screenName"
          [validationEnabled]="true"
          [required]="true"
          placeholder="Enter ID"
          (valueChange)="onIdValueChange($event)"
          (validationChange)="onIdValidationChange($event)">
        </app-id-dropdown>

        <!-- زر الإضافة -->
        <button mat-raised-button color="primary" type="button" 
                (click)="onAddClick()" 
                matTooltip="Add"
                class="initial-input-button add-button">
          <mat-icon>add</mat-icon>
        </button>

        <!-- زر التعديل -->
        <button mat-raised-button color="primary" type="button" 
                (click)="onEditClick()" 
                matTooltip="Edit"
                class="initial-input-button edit-button">
          <mat-icon>edit</mat-icon>
        </button>

        <!-- زر العرض -->
        <button mat-raised-button color="accent" type="button" 
                (click)="onViewClick()" 
                matTooltip="View"
                class="initial-input-button view-button">
          <mat-icon>visibility</mat-icon>
        </button>

        <!-- زر الصيانة -->
        <button mat-raised-button color="warn" type="button" 
                (click)="onMaintenanceClick()"
                matTooltip="Maintenance"
                class="initial-input-button maintenance-button">
          <mat-icon>build</mat-icon>
        </button>
      </div>
    </div>
  </form>
</div>
